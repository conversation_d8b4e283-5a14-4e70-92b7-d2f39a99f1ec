# 配置参数
$SQLInstance = "localhost\SQLEXPRESS"
$DatabaseName = "ZR"
$BaseBackupPath = "D:\SQLBackups\"  # 备份根目录
$LogFile = "D:\SQLBackupLogs\ZR_Backup_$(Get-Date -Format 'yyyyMMdd').log"
$RetentionDays = 14  # 保留14天的备份

# 仅从系统环境变量获取远程计算机名称，如果不存在则使用默认值
$RemoteComputer = [Environment]::GetEnvironmentVariable("SQL_BACKUP_REMOTE_COMPUTER", [EnvironmentVariableTarget]::Machine)
if ([string]::IsNullOrEmpty($RemoteComputer)) {
    $RemoteComputer = "desktop-a" # 如果系统环境变量不存在，使用默认值
    Write-Output "未找到系统环境变量 SQL_BACKUP_REMOTE_COMPUTER，使用默认值: $RemoteComputer"
}
Write-Output "使用备份目标计算机: $RemoteComputer"

# 添加网络备份位置配置
$RemoteSharePath = "\\$RemoteComputer\SQLBackups"  # 确保在目标计算机上已共享此文件夹

# 测试网络连接和共享访问的函数
function Test-NetworkShare {
    param (
        [string]$SharePath
    )

    try {
        if (Test-Path $SharePath) {
            Write-Output "网络共享路径可访问: $SharePath"
            return $true
        }
        else {
            Write-Output "无法访问网络共享路径: $SharePath"
            return $false
        }
    }
    catch {
        Write-Output "测试网络共享时出错: $_"
        return $false
    }
}

# 复制备份文件到网络位置的函数
function Copy-BackupToNetwork {
    param (
        [string]$SourcePath,
        [string]$DestinationPath
    )

    try {
        # 创建目标文件夹（如果不存在）
        if (-not (Test-Path $DestinationPath)) {
            New-Item -ItemType Directory -Path $DestinationPath -Force
            Write-Output "创建网络备份目录: $DestinationPath"
        }

        # 获取源文件名
        $fileName = Split-Path -Path $SourcePath -Leaf
        $destinationFilePath = Join-Path -Path $DestinationPath -ChildPath $fileName

        # 检查目标位置是否已存在该文件
        if (Test-Path $destinationFilePath) {
            Write-Output "远程主机上已存在文件: $destinationFilePath，跳过复制"
            return $true
        }
        else {
            # 复制文件
            Copy-Item -Path $SourcePath -Destination $DestinationPath -Force
            Write-Output "成功复制备份到网络位置: $destinationFilePath"
            return $true
        }
    }
    catch {
        Write-Output "复制备份到网络位置失败: $_"
        return $false
    }
}

# 同步备份文件夹的函数
function Sync-BackupFolders {
    param (
        [string]$LocalPath,
        [string]$RemotePath,
        [int]$SyncDays = 7  # 默认同步最近7天的备份
    )

    # 检查本地路径是否存在
    if (-not (Test-Path $LocalPath)) {
        Write-Output "本地路径不存在: $LocalPath"
        return
    }

    # 确保远程路径存在
    if (-not (Test-Path $RemotePath)) {
        New-Item -ItemType Directory -Path $RemotePath -Force
        Write-Output "创建远程路径: $RemotePath"
    }

    # 获取本地文件夹中的所有文件（仅限最近指定天数内的文件）
    $cutoffDate = (Get-Date).AddDays(-$SyncDays)
    $localFiles = Get-ChildItem -Path $LocalPath -File -Recurse |
    Where-Object { $_.LastWriteTime -ge $cutoffDate }

    # 计数器
    $totalFiles = $localFiles.Count
    $copiedFiles = 0
    $skippedFiles = 0
    $failedFiles = 0

    Write-Output "开始同步最近 $SyncDays 天内的 $totalFiles 个文件..."

    # 遍历每个本地文件
    foreach ($file in $localFiles) {
        # 获取相对路径
        $relativePath = $file.FullName.Substring($LocalPath.Length)

        # 构建远程文件路径
        $remoteFilePath = Join-Path -Path $RemotePath -ChildPath $relativePath
        $remoteFileDir = Split-Path -Path $remoteFilePath -Parent

        # 检查远程文件是否存在
        if (Test-Path $remoteFilePath) {
            Write-Verbose "远程文件已存在，跳过: $remoteFilePath"
            $skippedFiles++
            continue
        }

        # 确保远程目录存在
        if (-not (Test-Path $remoteFileDir)) {
            New-Item -ItemType Directory -Path $remoteFileDir -Force | Out-Null
            Write-Output "创建远程目录: $remoteFileDir"
        }

        # 复制文件
        try {
            Copy-Item -Path $file.FullName -Destination $remoteFilePath -Force
            Write-Output "成功复制文件: $($file.FullName) -> $remoteFilePath"
            $copiedFiles++
        }
        catch {
            Write-Output "复制文件失败: $($file.FullName) -> $remoteFilePath, 错误: $_"
            $failedFiles++
        }
    }

    # 输出统计信息
    if ($copiedFiles -gt 0 -or $failedFiles -gt 0) {
        Write-Output "同步完成。总文件数: $totalFiles, 已复制: $copiedFiles, 已跳过: $skippedFiles, 失败: $failedFiles"
    }
    else {
        Write-Output "同步完成。所有文件($totalFiles)已同步，无需额外复制。"
    }
}

# 备份类型枚举
enum BackupType {
    Full        # 完整备份
    Differential # 差异备份
    Log         # 日志备份
}

# 确定当前需要执行的备份类型
function Get-BackupType {
    # 获取当前日期和本周开始日期
    $currentDate = Get-Date
    $startOfWeek = $currentDate.Date.AddDays( - ([int]$currentDate.DayOfWeek))

    # 检查本周是否已经执行过完整备份
    $weeklyFullBackup = Get-ChildItem -Path $BaseBackupPath -Recurse -Filter "*_Full_*.bak" |
    Where-Object { $_.CreationTime -ge $startOfWeek } |
    Sort-Object CreationTime -Descending |
    Select-Object -First 1

    # 如果本周没有完整备份，执行完整备份
    if ($null -eq $weeklyFullBackup) {
        Write-Output "本周尚未执行完整备份，将执行完整备份..."
        return [BackupType]::Full
    }

    # 检查今天是否已经执行过差异备份
    $todayStart = $currentDate.Date
    $todayDiffBackup = Get-ChildItem -Path $BaseBackupPath -Recurse -Filter "*_Diff_*.bak" |
    Where-Object { $_.CreationTime -ge $todayStart } |
    Sort-Object CreationTime -Descending |
    Select-Object -First 1

    # 如果今天没有差异备份，执行差异备份
    if ($null -eq $todayDiffBackup) {
        Write-Output "今天尚未执行差异备份，将执行差异备份..."
        return [BackupType]::Differential
    }

    # 其他情况执行日志备份
    Write-Output "执行常规日志备份..."
    return [BackupType]::Log
}

# 获取周文件夹和日期文件夹名称的函数
function Get-BackupFolderNames {
    $currentDate = Get-Date
    $currentCulture = [System.Globalization.CultureInfo]::CurrentCulture
    $weekNumber = $currentCulture.Calendar.GetWeekOfYear(
        $currentDate,
        $currentCulture.DateTimeFormat.CalendarWeekRule,
        $currentCulture.DateTimeFormat.FirstDayOfWeek
    )

    # 获取本周的开始日期
    $startOfWeek = $currentDate.Date.AddDays( - ([int]$currentDate.DayOfWeek))

    # 计算当前是本周的第几天（周日为第1天）
    $dayOfWeek = [int]$currentDate.DayOfWeek + 1

    # 创建文件夹名称
    $weekFolderName = "$($currentDate.ToString('yyyy'))_week$($weekNumber.ToString('00'))"
    $dayFolderName = "day$dayOfWeek"

    return @{
        WeekFolder  = $weekFolderName
        DayFolder   = $dayFolderName
        StartOfWeek = $startOfWeek
    }
}

# 创建必要目录
if (-not (Test-Path $BaseBackupPath)) { New-Item -ItemType Directory -Path $BaseBackupPath -Force }
if (-not (Test-Path (Split-Path $LogFile))) { New-Item -ItemType Directory -Path (Split-Path $LogFile) -Force }

# 开始记录日志
Start-Transcript -Path $LogFile -Append
Write-Output "备份开始于: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

try {
    # 验证网络共享是否可访问
    if (-not (Test-NetworkShare -SharePath $RemoteSharePath)) {
        throw "无法访问网络备份位置，但将继续执行本地备份"
    }

    # 加载SQL Server模块
    try {
        Import-Module "C:\Users\<USER>\Documents\PowerShell\Modules\SqlServer" -ErrorAction Stop
    }
    catch {
        Import-Module SQLPS -DisableNameChecking -ErrorAction Stop
    }

    # 获取备份文件夹名称
    $folderInfo = Get-BackupFolderNames

    # 创建本地备份路径
    $WeeklyBackupPath = Join-Path -Path $BaseBackupPath -ChildPath $folderInfo.WeekFolder
    $DailyBackupPath = Join-Path -Path $WeeklyBackupPath -ChildPath $folderInfo.DayFolder

    # 创建网络备份路径
    $RemoteWeeklyBackupPath = Join-Path -Path $RemoteSharePath -ChildPath $folderInfo.WeekFolder
    $RemoteDailyBackupPath = Join-Path -Path $RemoteWeeklyBackupPath -ChildPath $folderInfo.DayFolder

    # 创建必要的目录
    if (-not (Test-Path $WeeklyBackupPath)) {
        New-Item -ItemType Directory -Path $WeeklyBackupPath -Force
        Write-Output "创建周备份目录: $WeeklyBackupPath"
    }

    if (-not (Test-Path $DailyBackupPath)) {
        New-Item -ItemType Directory -Path $DailyBackupPath -Force
        Write-Output "创建每日备份目录: $DailyBackupPath"
    }

    # 获取当前应执行的备份类型
    $backupType = Get-BackupType
    $TimeStamp = Get-Date -Format "HHmmss"

    # 连接字符串
    $connectionString = "Server=$SQLInstance;Integrated Security=True;TrustServerCertificate=True"

    # 在备份前确保数据库处于完整恢复模式
    $SetRecoveryQuery = @"
ALTER DATABASE [$DatabaseName]
SET RECOVERY FULL
"@
    Invoke-Sqlcmd -ConnectionString $connectionString -Query $SetRecoveryQuery

    switch ($backupType) {
        ([BackupType]::Full) {
            # 执行完整备份 - 保存在周文件夹下
            $BackupFile = "$WeeklyBackupPath\${DatabaseName}_Full_$TimeStamp.bak"
            Write-Output "执行完整备份..."
            $BackupQuery = @"
BACKUP DATABASE [$DatabaseName]
TO DISK = N'$BackupFile'
WITH INIT, STATS = 10, CHECKSUM
"@
            Invoke-Sqlcmd -ConnectionString $connectionString -Query $BackupQuery -QueryTimeout 0
            Write-Output "完整备份完成: $BackupFile"

            # 复制到网络位置的周文件夹
            Copy-BackupToNetwork -SourcePath $BackupFile -DestinationPath $RemoteWeeklyBackupPath
        }

        ([BackupType]::Differential) {
            # 执行差异备份 - 保存在日文件夹下
            $BackupFile = "$DailyBackupPath\${DatabaseName}_Diff_$TimeStamp.bak"
            Write-Output "执行差异备份..."
            $BackupQuery = @"
BACKUP DATABASE [$DatabaseName]
TO DISK = N'$BackupFile'
WITH DIFFERENTIAL, STATS = 10, CHECKSUM
"@
            Invoke-Sqlcmd -ConnectionString $connectionString -Query $BackupQuery -QueryTimeout 0
            Write-Output "差异备份完成: $BackupFile"

            # 复制到网络位置的日文件夹
            Copy-BackupToNetwork -SourcePath $BackupFile -DestinationPath $RemoteDailyBackupPath
        }

        ([BackupType]::Log) {
            # 执行日志备份 - 保存在日文件夹下
            $BackupFile = "$DailyBackupPath\${DatabaseName}_Log_$TimeStamp.trn"
            Write-Output "执行事务日志备份..."
            $BackupQuery = @"
BACKUP LOG [$DatabaseName]
TO DISK = N'$BackupFile'
WITH STATS = 10, CHECKSUM
"@
            Invoke-Sqlcmd -ConnectionString $connectionString -Query $BackupQuery -QueryTimeout 0
            Write-Output "事务日志备份完成: $BackupFile"

            # 复制到网络位置的日文件夹
            Copy-BackupToNetwork -SourcePath $BackupFile -DestinationPath $RemoteDailyBackupPath
        }
    }

    # 清理本地旧备份
    Write-Output "正在清理本地旧备份..."
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $backupFolders = Get-ChildItem -Path $BaseBackupPath -Directory |
    Where-Object {
        # 解析文件夹名称中的年份
        if ($_.Name -match '(\d{4})-week\d{2}') {
            $year = [int]$matches[1]
            $folderDate = [DateTime]::ParseExact("$year-01-01", "yyyy-MM-dd", $null)
            return $folderDate -lt $cutoffDate
        }
        return $false
    } |
    Sort-Object CreationTime

    foreach ($folder in $backupFolders) {
        try {
            Remove-Item -Path $folder.FullName -Recurse -Force
            Write-Output "已删除超过${RetentionDays}天的本地备份文件夹: $($folder.Name)"
        }
        catch {
            Write-Output "删除本地旧备份文件夹失败: $($folder.Name), 错误: $_"
        }
    }

    # 清理网络位置的旧备份（使用相同的逻辑）
    if (Test-NetworkShare -SharePath $RemoteSharePath) {
        Write-Output "正在清理网络位置的旧备份..."
        $remoteBackupFolders = Get-ChildItem -Path $RemoteSharePath -Directory |
        Where-Object {
            if ($_.Name -match '(\d{4})-week\d{2}') {
                $year = [int]$matches[1]
                $folderDate = [DateTime]::ParseExact("$year-01-01", "yyyy-MM-dd", $null)
                return $folderDate -lt $cutoffDate
            }
            return $false
        } |
        Sort-Object CreationTime
        foreach ($folder in $remoteBackupFolders) {
            try {
                Remove-Item -Path $folder.FullName -Recurse -Force
                Write-Output "已删除超过${RetentionDays}天的网络备份文件夹: $($folder.Name)"
            }
            catch {
                Write-Output "删除网络旧备份文件夹失败: $($folder.Name), 错误: $_"
            }
        }
    }

    Write-Output "备份操作成功完成，本地和网络位置均保留最近 $RetentionDays 天的备份"

    # 在备份操作完成后，执行一次全面的同步检查
    if (Test-NetworkShare -SharePath $RemoteSharePath) {
        Write-Output "`n开始执行备份文件同步检查..."
        # 同步最近7天的备份文件（可根据需要调整天数）
        Sync-BackupFolders -LocalPath $BaseBackupPath -RemotePath $RemoteSharePath -SyncDays 7
        Write-Output "备份文件同步检查完成"
    }
    else {
        Write-Output "无法访问网络备份位置，跳过同步检查"
    }
}
catch {
    Write-Output "错误发生: $_"
    $errorMessage = $_.Exception.Message
    Write-Output "错误详情: $errorMessage"

    # 即使主备份过程出错，也尝试进行同步
    if (Test-NetworkShare -SharePath $RemoteSharePath) {
        Write-Output "`n尽管备份过程出错，仍尝试执行备份文件同步..."
        Sync-BackupFolders -LocalPath $BaseBackupPath -RemotePath $RemoteSharePath -SyncDays 7
        Write-Output "备份文件同步尝试完成"
    }
}
finally {
    Stop-Transcript
}
